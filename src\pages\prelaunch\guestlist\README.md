# Guest List Infinite Scrolling Implementation

## Overview

This implementation adds infinite scrolling pagination to the guest list in the GuestTab component. When a user scrolls to the bottom of the guest list, it automatically fetches the next page of guests (5 at a time) and appends them to the existing list.

## Components Modified

### 1. `guest-tab.tsx`
- **Added Props**: 
  - `onLoadMore`: Function to trigger loading more guests
  - `isLoadingMore`: Boolean indicating if more guests are being loaded
  - `hasMorePages`: Boolean indicating if there are more pages available

- **New Features**:
  - Scroll detection using `useRef` and scroll event listeners
  - Loading indicator for infinite scroll
  - End of list indicator when no more pages are available
  - Maintains filtering by status (Confirmed, Pending, Declined)

### 2. `guestlist.tsx`
- **Updated Hook**: Changed from `useGuests` to `useInfiniteGuests`
- **New Data Flow**: Now manages accumulated guest data and pagination state
- **Updated Stats**: Guest counts are calculated from the accumulated data

### 3. `useGuestListManagement.ts`
- **New Hook**: `useInfiniteGuests` for managing infinite scrolling
- **Features**:
  - Accumulates guest data from multiple API calls
  - Manages pagination state (current page, loading states)
  - Provides `loadMore` function for fetching additional pages
  - Maintains total guest count and metadata

## How It Works

1. **Initial Load**: The `useInfiniteGuests` hook loads the first page of guests (5 guests)
2. **Scroll Detection**: The `GuestTab` component listens for scroll events on the guest list container
3. **Threshold Check**: When the user scrolls within 100px of the bottom, it triggers `onLoadMore`
4. **Load More**: The hook fetches the next page and appends new guests to the existing list
5. **Status Filtering**: Client-side filtering maintains the current tab's status filter
6. **Loading States**: Shows loading indicators and end-of-list messages appropriately

## Configuration

- **Page Size**: 5 guests per page (configurable in `useInfiniteGuests`)
- **Scroll Threshold**: 100px from bottom (configurable in `handleScroll`)
- **Max Height**: 600px for the scrollable container

## API Integration

The implementation uses the existing `events.getGuestsForAnAuthUser` API with pagination parameters:
- `page`: Current page number
- `per_page`: Number of guests per page (5)

## Testing

A test suite is included in `__tests__/infinite-scroll.test.tsx` that covers:
- Guest rendering
- Loading states
- Status filtering
- Scroll-triggered loading

## Usage Example

```tsx
<GuestTab 
  guests={guests} 
  onLoadMore={loadMore}
  isLoadingMore={isLoadingMore}
  hasMorePages={hasMorePages}
/>
```

## Performance Considerations

- **Client-side Filtering**: Guests are filtered by status on the client side after loading
- **Memory Usage**: All loaded guests are kept in memory for fast filtering
- **Scroll Performance**: Uses throttled scroll events to prevent excessive API calls

## Future Improvements

1. **Server-side Filtering**: Implement status filtering on the API level
2. **Virtual Scrolling**: For very large guest lists, implement virtual scrolling
3. **Caching**: Add intelligent caching for previously loaded pages
4. **Error Handling**: Enhanced error handling for failed pagination requests
