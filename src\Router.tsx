import { LoginPage } from './pages/login/login';
import { SignupPage } from './pages/signup/signup';
import { createBrowserRouter } from 'react-router-dom';
import { NotFound } from './pages/notFound/notFound';
import {
  AuthRoute,
  ProtectedRoute,
  OnboardingRoute,
} from './lib/protectedRoutes';
import { OAuthCallback } from './pages/signup/oauth-callback';
import { PrelaunchDashboard } from './pages/prelaunch/prelaunch-dashboard/dashboard';
import { Home } from './pages/prelaunch/prelaunch-dashboard/home';
import { ForgotPassword } from './pages/forgotPassword/forgot-password';
import { Settings } from './pages/settings/setting';
import { OnboardingQuestions } from './pages/onboarding/onboarding-questions';
import { Registering } from './pages/prelaunch/register-as-guest/registering';
import { GuestList } from './pages/prelaunch/guestlist/guestlist';
import { GiftRegistry } from './pages/prelaunch/gift-registry/gift-registry/gift-registry';
import { GiftRegistryDetails } from './pages/prelaunch/gift-registry/gift-registry-details/gift-registry-details';
import { GiftItemDetails } from './pages/prelaunch/gift-registry/gift-item-details/gift-item-details';
import { CashGiftDetails } from './pages/prelaunch/gift-registry/cash-gift-details/cash-gift-details';
import { CashGiftReservation } from './pages/prelaunch/gift-registry/purchasing-gifts/cash-gift-reservation';
import {
  SelectAccount,
  WithdrawalAmount,
  AddBankDetails,
  Authenticate,
} from './pages/prelaunch/gift-registry/withdrawal-flow';
import { BackFromJumia } from './pages/prelaunch/gift-registry/purchasing-gifts/back-from-jumia';
import { ViewingGiftAsGuest } from './pages/prelaunch/gift-registry/gift-item-as-guest/viewing-gift-as-guest';

export const router = createBrowserRouter([
  {
    element: <AuthRoute />,
    children: [
      {
        path: '/login',
        element: <LoginPage />,
      },

      {
        path: '/signup',
        element: <SignupPage />,
      },
      {
        path: '/guest-registry',
        element: <Registering />,
      },
      {
        path: '/forgot-password',
        element: <ForgotPassword />,
      },

      {
        path: '/oauth',
        element: <OAuthCallback />,
      },
    ],
  },

  {
    path: '/onboarding',
    children: [
      {
        index: true,
        element: (
          <OnboardingRoute>
            <OnboardingQuestions />
          </OnboardingRoute>
        ),
      },
    ],
  },

  {
    errorElement: (
      <div className="flex justify-center items-center h-screen">
        Sorry, an error occured. Kindly refresh the page.
      </div>
    ),
    element: <PrelaunchDashboard />,
    children: [
      {
        path: '/',
        children: [
          {
            index: true,
            element: <Home />,
          },
        ],
      },
      {
        path: '/guest-lists',
        element: <GuestList />,
      },
      {
        path: '/gift-registry',
        element: <GiftRegistry />,
      },
      {
        path: '/budget-planner',
        element: <div className="py-68">BUDGET PLANNER</div>,
      },
    ],
  },
  {
    path: '/gift-registry/:id',
    element: (
      <ProtectedRoute>
        <GiftRegistryDetails />
      </ProtectedRoute>
    ),
  },
  {
    path: '/gift-registry/:registryId/gift/:giftId',
    element: (
      <ProtectedRoute>
        <GiftItemDetails />
      </ProtectedRoute>
    ),
  },
  {
    path: '/back-from-jumia',
    element: (
      <ProtectedRoute>
        <BackFromJumia />
      </ProtectedRoute>
    ),
  },
  {
    path: '/giftItem-as-guest',
    element: (
      <ProtectedRoute>
        <ViewingGiftAsGuest />
      </ProtectedRoute>
    ),
  },
  {
    path: '/gift-registry/:registryId/cash/:cashId',
    element: (
      <ProtectedRoute>
        <CashGiftDetails />
      </ProtectedRoute>
    ),
  },
  {
    path: '/gift-registry/:registryId/cash/:cashId/reserve',
    element: (
      <ProtectedRoute>
        <CashGiftReservation />
      </ProtectedRoute>
    ),
  },
  {
    path: '/withdrawal/select-account',
    element: (
      <ProtectedRoute>
        <SelectAccount />
      </ProtectedRoute>
    ),
  },
  {
    path: '/withdrawal/amount',
    element: (
      <ProtectedRoute>
        <WithdrawalAmount />
      </ProtectedRoute>
    ),
  },
  {
    path: '/withdrawal/add-bank-details',
    element: (
      <ProtectedRoute>
        <AddBankDetails />
      </ProtectedRoute>
    ),
  },
  {
    path: '/withdrawal/authenticate',
    element: (
      <ProtectedRoute>
        <Authenticate />
      </ProtectedRoute>
    ),
  },
  {
    path: '/settings',
    element: (
      <ProtectedRoute>
        <Settings />
      </ProtectedRoute>
    ),
  },
  {
    path: '*',
    element: <NotFound />,
    errorElement: (
      <div className="flex justify-center items-center h-screen">
        Sorry, an error occured. Kindly refresh the page.
      </div>
    ),
  },
]);
