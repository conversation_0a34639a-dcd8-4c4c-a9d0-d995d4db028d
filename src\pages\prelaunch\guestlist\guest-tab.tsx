/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react';
import { SearchStatus, Filter } from 'iconsax-react';

export const GuestTab = ({ guests }: any) => {
  const [activeTab, setActiveTab] = useState('Confirmed');
  const [hoveredCardId, setHoveredCardId] = useState<number | null>(null);

  const getFilteredGuests = (guests: any[], status: string) => {
    return guests?.filter(
      (guest) => guest?.invite_status.toLowerCase() === status.toLowerCase()
    );
  };

  const displayedGuests =
    activeTab === 'Confirmed'
      ? getFilteredGuests(guests, 'confirmed')
      : activeTab === 'Pending'
      ? getFilteredGuests(guests, 'pending')
      : activeTab === 'Declined'
      ? getFilteredGuests(guests, 'declined')
      : [];

  return (
    <div className=" mb-16">
      <div className="flex justify-between items-center mb-6 relative">
        <div className="flex bg-black/3 rounded-full p-1.5">
          {['Confirmed', 'Pending', 'Declined'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`md:px-4 px-1 py-1 md:py-[9px] cursor-pointer rounded-full text-sm  ${
                activeTab === tab
                  ? 'bg-white text-primary font-bold'
                  : 'text-grey-250 font-medium'
              }`}>
              {tab}
            </button>
          ))}
        </div>
        <div
          className={`absolute bottom-[-10px] w-1 h-1 bg-cus-orange rounded-full ${
            activeTab === 'Confirmed'
              ? 'left-[50px]'
              : activeTab === 'Pending'
              ? 'md:left-[142px] left-[108px]'
              : 'md:left-[228px] left-[170px]'
          }`}
        />
        <div className="flex space-x-3">
          <button className="md:h-12 md:w-12 h-7 w-7 rounded-full bg-white flex items-center justify-center">
            <SearchStatus size={26} color="#4D55F2" variant="Bulk" />
          </button>
          <button className="md:h-12 md:w-12  h-7 w-7 rounded-full bg-white flex items-center justify-center">
            <Filter size={26} color="#5F66F3" variant="Bulk" />
          </button>
        </div>
      </div>

      <div className="space-y-4 ">
        {displayedGuests?.length === 0 ? (
          <div className="text-center py-10 text-grey-600">
            <div className="text-2xl font-semibold mb-2">No Guests Found</div>
            <p className="text-sm text-grey-500">
              There are no guests under the <strong>{activeTab}</strong> tab.
            </p>
          </div>
        ) : (
          displayedGuests?.map((guest) => (
            <div
              key={guest?.id}
              className="p-4 bg-white  border border-grey-150 rounded-2xl md:flex justify-between items-center"
              onMouseEnter={() => setHoveredCardId(guest?.id)}
              onMouseLeave={() => setHoveredCardId(null)}>
              <div className="flex items-center">
                <div
                  className={`w-10 h-10 bg-gradient-to-br from-[#FEF7F4] capitalize rounded-full flex items-center justify-center mr-4 text-dark-blue-200 font-semibold text-base`}>
                  {`${guest?.first_name.charAt(0)}${guest?.last_name.charAt(
                    0
                  )}`}
                </div>
                <div>
                  <h3
                    className={`text-dark-blue-100 capitalize  text-sm font-semibold`}>
                    {`${guest?.first_name} ${guest?.last_name}`}
                  </h3>
                  <p className="text-grey-650 text-xs">
                    {guest.email}{' '}
                    {guest?.phone_number && `• ${guest?.phone_number}`}
                  </p>
                </div>
              </div>
              <div className="flex items-start justify-end mt-5 md:mt-0">
                <div className="flex flex-col items-end mr-2">
                  <span
                    className={`${
                      guest?.status === 'Attending'
                        ? 'text-green-600 bg-grin '
                        : guest?.status === 'Pending'
                        ? 'text-cus-orange-600 bg-cus-pink-150 '
                        : 'text-cus-red-100 bg-cus-pink-250'
                    } font-medium px-2.5 py-0.5 rounded-2xl text-sm`}>
                    {guest?.invite_status}
                  </span>
                  <span className="text-grey-650 text-[10px] italic text-right md:text-start">
                    {guest?.invite_type === 'manual'
                      ? 'Added Manually'
                      : 'Added Via Email invite'}
                  </span>
                </div>
                {hoveredCardId === guest?.id && (
                  <button className="w-6 h-6 cursor-pointer rounded-full bg-gray-100 flex items-center justify-center transition-all duration-500 ease-in-out">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 32 32"
                      fill="#999999">
                      <circle cx="8" cy="16" r="3" />
                      <circle cx="16" cy="16" r="3" />
                      <circle cx="24" cy="16" r="3" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
