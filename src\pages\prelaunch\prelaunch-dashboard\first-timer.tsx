import { ArrowDown2 } from 'iconsax-react';
import box from '../../../assets/images/box1.png';
import ribbon from '../../../assets/images/ribbon.png';
import graph from '../../../assets/images/gra-ph.png';
import { Button } from '../../../components/button/onboardingButton';
import { Modal } from '../../../components/reuseables/prelaunch';
import { useState } from 'react';
import { motion } from 'motion/react';
import { modalVariants } from '../../../components/reuseables/animations/animations';
import gift from '../../../assets/animations/gift.gif';
import gift1 from '../../../assets/images/gift1.png';
import { SetGiftRegistry } from '../gift-registry/new-user/set-gift-registry';

export const FirstTimer = () => {
  const [isFirstModalOpen, setIsFirstModalOpen] = useState(false);
  const [isSecondModalOpen, setIsSecondModalOpen] = useState(false);
  const [showGiftRegistry, setShowGiftRegistry] = useState(false);
  
  const openFirstModal = () => {
    setIsFirstModalOpen(true);
    setIsSecondModalOpen(false);
  };
  
  const openSecondModal = () => {
    setIsFirstModalOpen(false);
    setIsSecondModalOpen(true);
  };
  
  const closeAllModals = () => {
    setIsFirstModalOpen(false);
    setIsSecondModalOpen(false);
  };
  
  const navigateToGiftRegistrySetup = () => {
    closeAllModals();
    setShowGiftRegistry(true);
  };
  
  return (
    <div className="px-4 md:px-0">
      <div className="flex flex-col md:flex-row gap-6 md:gap-0 justify-between mb-5">
        <div className="bg-white shadow-[0_12px_120px_0_#5F5F5F0F] rounded-[14px] p-4 md:max-w-[270px] w-full h-[320px] relative overflow-hidden">
          <div className="relative z-10  h-full ">
            <h2 className="text-sm font-medium text-grey-250 tracking-[0.10em]">
              GIFT REGISTRY
            </h2>
            <p className="text-2xl mt-3 mb-5 font-medium">
              Curate{' '}
              <span className="italic text-transparent bg-clip-text bg-[linear-gradient(120.36deg,#FFBBA3_28.82%,#A6AAF9_67.72%)]">
                Gifts
              </span>{' '}
              for <br />
              your event
            </p>
            <Button
              onClick={openFirstModal}
              variant="primary"
              size="md"
              className="bg-primary h-9 text-white">
              Create Registry{' '}
            </Button>{' '}
          </div>
          <div className="absolute -bottom-7 blur-xs right-0 pointer-events-none ">
            <img src={box} alt="box" />
          </div>
        </div>
        <div className="bg-white shadow-[0_12px_120px_0_#5F5F5F0F] rounded-[14px] p-4 md:max-w-[270px] w-full h-[320px] relative overflow-hidden">
          <div className="relative z-10  h-full ">
            <h2 className="text-sm font-medium text-grey-250 tracking-[0.10em]">
              GUESTS
            </h2>
            <p className="text-2xl mt-3 mb-5 font-medium">
              Create a Guestlist
              <br /> for your event
            </p>
            <Button
              variant="primary"
              size="md"
              className="bg-[linear-gradient(190.72deg,#FF9975_25.22%,#FF5519_89.64%)] h-9 text-white">
              Create Guest list{' '}
            </Button>{' '}
          </div>
          <div className="absolute bottom-0 right-0 pointer-events-none ">
            <img src={ribbon} alt="box" />
          </div>
        </div>
      </div>
      <div className="w-full mb-40 flex justify-center text-center sm:text-left sm:justify-between items-start max-w-[560px] sm:h-[205px] bg-white rounded-[14px]  ">
        <div className="pl-3 pt-3 pb-3 sm:pb-0">
          <div className="text-primary italic font-extrabold text-xs gap-1 mb-2 flex items-center">
            BUDGET PLANNER
            <ArrowDown2 color="#292D32" size={12} />
          </div>
          <p className="md:text-[28px] font-medium">
            Curate{' '}
            <span className="italic text-transparent bg-clip-text bg-[linear-gradient(120.36deg,#FFBBA3_28.82%,#A6AAF9_67.72%)]">
              Gifts
            </span>{' '}
            for <br />
            your event
          </p>

          <button
            type="button"
            className="mt-2 bg-gradient-to-r from-[#4D55F2] to-[#FF6630] rounded-full ">
            <div className="rounded-full bg-white border border-dashed text-nowrap text-sm font-semibold px-2 py-1">
              Create A Budget
            </div>
          </button>
        </div>
        <img
          src={graph}
          alt="graph"
          className="rounded-tr-[14px] hidden sm:block"
        />
      </div>
      <Modal
        isOpen={isFirstModalOpen}
        onClose={closeAllModals}
        actionButton={openSecondModal}
        forwardActionText="Continue"
        thirdModalAnimation={true}
        title="Gift"
        name="Registry"
        subtitle="Wish. Share. Receive."
        description={
          <p>
            Create the perfect wishlist, share it with <br /> loved ones, and
            receive gifts you truly want.
            <br /> No duplicates, no guesswork—just joy!
          </p>
        }
        leftContent={
          <div className="relative overflow-hidden rounded-l-[20px] rounded-r-[20px] md:rounded-r-none h-[505px] md:h-full bg-[linear-gradient(179.93deg,#343CD8_0.06%,#000040_39.43%)]">
            <img
              src={gift}
              alt="gift"
              className="h-full w-full object-cover opacity-40"
            />
            <motion.img
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={modalVariants}
              src={gift1}
              alt="gift decoration"
              className="absolute z-50 top-18 left-0 rounded-l-[20px]  h-full w-full object-contain"
            />
          </div>
        }
        leftContentMobile={true}
      />
      <Modal
        isOpen={isSecondModalOpen}
        onClose={closeAllModals}
        actionButton={navigateToGiftRegistrySetup}
        forwardActionText="Continue"
        thirdModalAnimation={true}
        title="Gift"
        name="Registry"
        subtitle="WHAT TO EXPECT"
        description={
          <div className="mt-4">
            <p className="mb-4">Here are some steps to follow to set up your gift registry</p>
            
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center text-sm font-medium">1</div>
                <p className="text-sm text-gray-800">Connect your account to be able to receive and make payment</p>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center text-sm font-medium">2</div>
                <p className="text-sm text-gray-800">Set up your Wallet</p>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center text-sm font-medium">3</div>
                <p className="text-sm text-gray-800">Enter address for delivery of items purchased</p>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center text-sm font-medium">4</div>
                <p className="text-sm text-gray-800">Start Curating gifts</p>
              </div>
            </div>
          </div>
        }
        leftContent={
          <div className="relative overflow-hidden rounded-l-[20px] rounded-r-[20px] md:rounded-r-none h-[505px] md:h-full bg-[linear-gradient(179.93deg,#343CD8_0.06%,#000040_39.43%)]">
            <img
              src={gift}
              alt="gift"
              className="h-full w-full object-cover opacity-40"
            />
            <motion.img
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={modalVariants}
              src={gift1}
              alt="gift decoration"
              className="absolute z-50 top-18 left-0 rounded-l-[20px]  h-full w-full object-contain"
            />
          </div>
        }
        leftContentMobile={true}
      />
      {showGiftRegistry && <SetGiftRegistry onClose={() => setShowGiftRegistry(false)} />}
    </div>
  );
};
