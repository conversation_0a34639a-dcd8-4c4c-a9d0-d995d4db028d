import { ArrowCircleRight2, <PERSON><PERSON><PERSON> } from 'iconsax-react';
import { useState, useEffect } from 'react';
import { Button } from '../../components/button/onboardingButton';
import { Onboarding } from '../prelaunch/onboarding';
import { motion, AnimatePresence } from 'framer-motion';
import {
  cardSlideInVariants,
  fadeInRightTextVariants,
  fadeInRightLetterVariants,
} from '../../components/reuseables/animations/animations';
import { useMutation } from '@tanstack/react-query';
import { AuthServices } from '../../lib/services/auth';
import { useUserAuthStore } from '../../lib/store/auth';
import { toast } from 'react-toastify';

interface InviteCollaboratorsProps {
  onNext: (collaborators: string[]) => void;
  initialData?: string[];
  eventName?: string;
}

export const InviteCollaborators = ({
  onNext,
  initialData,
  eventName,
}: InviteCollaboratorsProps) => {
  const { userData } = useUserAuthStore();
  const [emails, setEmails] = useState<string[]>(initialData || []);
  const [inputValue, setInputValue] = useState('');
  const [lastStep, setLastStep] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const completeOnboardingMutation = useMutation({
    mutationFn: () =>
      AuthServices.updateAuthenticatedUser({
        config: {
          completed_onboarding: true,
        },
        first_name: userData?.first_name || '',
        last_name: userData?.last_name || '',
      }),

    onError: () => {
      toast.error('Failed to complete onboarding. Please try again.');
    },
  });

  useEffect(() => {
    if (initialData) {
      setEmails(initialData);
    }
  }, [initialData]);

  const validateAndAddEmail = (email: string) => {
    const trimmedEmail = email.trim();
    if (!trimmedEmail) {
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(trimmedEmail)) {
      setError('Please enter a valid email address');
      return;
    }

    if (emails.includes(trimmedEmail)) {
      setError('This email has already been added');
      return;
    }

    const newEmails = [...emails, trimmedEmail];
    setEmails(newEmails);

    onNext(newEmails);

    setInputValue('');
    setError(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      validateAndAddEmail(inputValue);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    if (error) setError(null);
  };

  const removeEmail = (index: number) => {
    const newEmails = emails.filter((_, i) => i !== index);
    setEmails(newEmails);
    onNext(newEmails);
  };

  const handleSkip = () => {
    setEmails([]);
    onNext([]);
    setLastStep(true);
    completeOnboardingMutation.mutate();
  };

  const handleComplete = () => {
    onNext(emails);
    setLastStep(true);
    completeOnboardingMutation.mutate();
  };

  return (
    <div>
      <div className="flex justify-between items-end">
        <motion.h2
          className="md:text-[40px] mb-4 text-xl font-medium leading-[114.99999999999999%]"
          initial="hidden"
          animate="visible"
          variants={fadeInRightTextVariants}>
          <AnimatePresence mode="wait">
            {['Invite', 'a', 'partner', 'or'].map((word, i) => (
              <motion.span
                key={i}
                variants={fadeInRightLetterVariants}
                style={{
                  display: 'inline-block',
                  marginRight: '8px',
                  transformOrigin: 'left center',
                  position: 'relative',
                }}>
                {word}
                <motion.span
                  style={{
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    opacity: 0,
                  }}
                  initial={{ opacity: 0, x: -4 }}
                  animate={{ opacity: 0 }}
                  exit={{ opacity: 0.2, x: 4 }}>
                  {word}
                </motion.span>
              </motion.span>
            ))}
            <br />
            {['planner', 'now.'].map((word, i) => (
              <motion.span
                key={i}
                variants={fadeInRightLetterVariants}
                style={{
                  display: 'inline-block',
                  marginRight: '8px',
                  transformOrigin: 'left center',
                  position: 'relative',
                }}>
                {word}
                <motion.span
                  style={{
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    opacity: 0,
                  }}
                  initial={{ opacity: 0, x: -4 }}
                  animate={{ opacity: 0 }}
                  exit={{ opacity: 0.2, x: 4 }}>
                  {word}
                </motion.span>
              </motion.span>
            ))}
          </AnimatePresence>
        </motion.h2>
        <button
          type="button"
          onClick={handleSkip}
          disabled={completeOnboardingMutation.isPending}
          className="border border-cus-orange-150 cursor-pointer text-cus-orange-500 flex items-center gap-2 px-3 py-2 rounded-full text-sm font-semibold disabled:opacity-50 disabled:cursor-not-allowed">
          <span>
            {completeOnboardingMutation.isPending ? 'Completing...' : 'Skip'}
          </span>
          <div className="bg-cus-orange-100/30 h-4 w-4 rounded-full flex justify-center items-center">
            {completeOnboardingMutation.isPending ? (
              <div className="animate-spin h-3 w-3 border border-orange-500 border-t-transparent rounded-full" />
            ) : (
              <ArrowRight color="#FF6630" size="10" />
            )}
          </div>
        </button>
      </div>

      <motion.div
        className="bg-white mt-4 px-5 pt-7 pb-6 rounded-[20px] shadow-[0px_12px_120px_0px_#5F5F5F0F]"
        initial="hidden"
        animate="visible"
        variants={cardSlideInVariants}>
        <div>
          <label className="block text-sm text-grey-500 font-medium mb-1.5">
            Invitee’s Email{' '}
          </label>
          <input
            type="email"
            placeholder="Enter Invitee’s email"
            className={`w-full pr-3.5 py-2.5 pl-2.5 rounded-full text-base text-grey-300 outline-none border ${
              error ? 'border-red-500' : 'border-grey-200'
            }`}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
          />
          {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
        </div>
        <p className="italic text-xs mt-1 text-grey-650">
          Hit Enter on your keyboard to add more emails to the list
        </p>
        {/* Email tags container */}
        <div className="flex flex-wrap gap-2 mt-4 pt-4">
          {emails.map((email, index) => (
            <div
              key={index}
              className="border border-grey-900 px-4 py-2 rounded-full flex items-center gap-2">
              <span className="text-grey text-xs font-medium">{email}</span>
              <button
                onClick={() => removeEmail(index)}
                className="text-grey-500 cursor-pointer hover:text-grey-700">
                ×
              </button>
            </div>
          ))}
        </div>
        <Button
          variant="primary"
          size="md"
          onClick={handleComplete}
          disabled={completeOnboardingMutation.isPending}
          className="text-white mt-13 bg-primary"
          iconRight={
            completeOnboardingMutation.isPending ? (
              <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full" />
            ) : (
              <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
            )
          }>
          {completeOnboardingMutation.isPending ? 'Completing...' : 'Complete'}
        </Button>
      </motion.div>
      {lastStep && <Onboarding eventName={eventName} />}
    </div>
  );
};
